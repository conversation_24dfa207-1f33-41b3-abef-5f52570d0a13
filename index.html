<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商家管理后台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .container {
            max-width: 375px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            position: relative;
        }
        
        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            margin-bottom: 15px;
        }
        
        .store-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .store-name {
            font-size: 18px;
            font-weight: bold;
        }
        
        .store-status {
            background: rgba(255, 255, 255, 0.2);
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
        }
        
        .stats-container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 15px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .score-circle {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            border: 3px solid #4CAF50;
        }
        
        .score-number {
            font-size: 24px;
            font-weight: bold;
        }
        
        .score-label {
            font-size: 10px;
            opacity: 0.8;
        }
        
        .stats-info {
            flex: 1;
            margin-left: 20px;
        }
        
        .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        
        .stat-label {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .stat-value {
            font-size: 16px;
            font-weight: bold;
        }
        
        .menu-grid {
            padding: 20px;
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 20px;
        }
        
        .menu-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: #333;
        }
        
        .menu-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 8px;
            font-size: 24px;
            color: white;
        }
        
        .menu-text {
            font-size: 12px;
            text-align: center;
        }
        
        .todo-section {
            margin: 20px;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .todo-header {
            display: flex;
            justify-content: space-between;
            padding: 15px 20px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .todo-title {
            font-weight: bold;
        }
        
        .todo-status {
            color: #999;
            font-size: 14px;
        }
        
        .ad-banner {
            margin: 20px;
            background: linear-gradient(135deg, #ff6b6b, #ffa500);
            border-radius: 12px;
            padding: 15px;
            color: white;
            position: relative;
            overflow: hidden;
        }
        
        .ad-text {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .ad-subtitle {
            font-size: 12px;
            opacity: 0.9;
        }
        
        .service-section {
            margin: 20px;
        }
        
        .section-tabs {
            display: flex;
            margin-bottom: 15px;
        }
        
        .tab {
            padding: 8px 0;
            margin-right: 30px;
            font-size: 16px;
            color: #666;
            border-bottom: 2px solid transparent;
        }
        
        .tab.active {
            color: #1890ff;
            border-bottom-color: #1890ff;
        }
        
        .service-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }
        
        .service-card {
            background: white;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .service-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .service-subtitle {
            font-size: 12px;
            color: #999;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="status-bar">
                <span>22:30</span>
                <span>🏠 📶 📶 📶 🔋 78</span>
            </div>
            
            <div class="store-info">
                <div class="store-name">中国兰州牛肉...</div>
                <div class="store-status">🔴 已打烊</div>
            </div>
            
            <div style="font-size: 12px; opacity: 0.8; margin-bottom: 15px;">
                到过营业时间自动恢复营业
            </div>
            
            <div class="stats-container">
                <div class="score-circle">
                    <div class="score-number">95</div>
                    <div class="score-label">店铺分</div>
                </div>
                <div class="stats-info">
                    <div class="stat-item">
                        <span class="stat-label">今日预计收入(元)</span>
                        <span class="stat-value">318.00</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">有效订单量(单)</span>
                        <span class="stat-value">27</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="menu-grid">
            <a href="#" class="menu-item">
                <div class="menu-icon" style="background: #4CAF50;">📦</div>
                <div class="menu-text">商品管理</div>
            </a>
            <a href="#" class="menu-item">
                <div class="menu-icon" style="background: #FF9800;">💬</div>
                <div class="menu-text">顾客评价</div>
            </a>
            <a href="#" class="menu-item">
                <div class="menu-icon" style="background: #2196F3;">🎁</div>
                <div class="menu-text">活动中心</div>
            </a>
            <a href="#" class="menu-item">
                <div class="menu-icon" style="background: #F44336;">📢</div>
                <div class="menu-text">店铺推广</div>
            </a>
            <a href="#" class="menu-item">
                <div class="menu-icon" style="background: #9C27B0;">⭐</div>
                <div class="menu-text">财务对账</div>
            </a>
            
            <a href="#" class="menu-item">
                <div class="menu-icon" style="background: #3F51B5;">📊</div>
                <div class="menu-text">数据中心</div>
            </a>
            <a href="#" class="menu-item">
                <div class="menu-icon" style="background: #009688;">🏪</div>
                <div class="menu-text">门店装修</div>
            </a>
            <a href="#" class="menu-item">
                <div class="menu-icon" style="background: #FFC107;">⚡</div>
                <div class="menu-text">流量活动</div>
            </a>
            <a href="#" class="menu-item">
                <div class="menu-icon" style="background: #607D8B;">🏃</div>
                <div class="menu-text">到店自取</div>
            </a>
            <a href="#" class="menu-item">
                <div class="menu-icon" style="background: #795548;">📧</div>
                <div class="menu-text">服务市场</div>
            </a>
        </div>
        
        <div class="todo-section">
            <div class="todo-header">
                <div class="todo-title">重要待办</div>
                <div class="todo-status">暂无待办</div>
            </div>
        </div>
        
        <div class="ad-banner">
            <div class="ad-text">1元起购饿了么官方打印机</div>
            <div class="ad-subtitle">平台百万补贴，快来抢福利</div>
            <div style="position: absolute; right: 15px; top: 50%; transform: translateY(-50%); font-size: 30px;">🖨️</div>
        </div>
        
        <div class="service-section">
            <div class="section-tabs">
                <div class="tab active">服务市场</div>
                <div class="tab">外卖课堂</div>
            </div>
            
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                <h3>服务市场</h3>
                <span style="color: #999; font-size: 14px;">查询已购商品 ></span>
            </div>
            
            <div class="service-grid">
                <div class="service-card">
                    <div class="service-title">精选好货 ></div>
                    <div class="service-subtitle">平台优选供应商</div>
                </div>
                <div class="service-card">
                    <div class="service-title">进入首页 ></div>
                    <div style="margin-top: 10px;">
                        <span style="background: #f0f0f0; padding: 2px 8px; border-radius: 4px; font-size: 10px; margin-right: 5px;">官方选</span>
                        <span style="background: #f0f0f0; padding: 2px 8px; border-radius: 4px; font-size: 10px;">鲜鸡蛋</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
